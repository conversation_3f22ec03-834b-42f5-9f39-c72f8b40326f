<?php

use Illuminate\Support\Facades\Route;

Route::view('/', 'welcome');

// Default dashboard (fallback)
Route::view('dashboard', 'dashboard')
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

// Role-based dashboards
Route::middleware(['auth', 'role:admin'])->group(function () {
    Route::view('admin/dashboard', 'admin.dashboard')->name('admin.dashboard');
    Route::view('admin/users', 'admin.users')->name('admin.users');
    Route::view('admin/branches', 'admin.branches')->name('admin.branches');
});

Route::middleware(['auth', 'role:manager'])->group(function () {
    Route::view('manager/dashboard', 'manager.dashboard')->name('manager.dashboard');
    Route::view('manager/members', 'manager.members')->name('manager.members');
    Route::view('manager/loans', 'manager.loans')->name('manager.loans');
    Route::view('manager/loan-approvals', 'manager.loan-approvals')->name('manager.loan-approvals');
    Route::view('manager/loan-management', 'manager.loan-management')->name('manager.loan-management');
    Route::view('manager/installment-reports', 'manager.installment-reports')->name('manager.installment-reports');
});

Route::middleware(['auth', 'role:field_officer'])->group(function () {
    Route::view('officer/dashboard', 'officer.dashboard')->name('officer.dashboard');
    Route::view('officer/collections', 'officer.collections')->name('officer.collections');
    Route::view('officer/members', 'officer.members')->name('officer.members');
    Route::view('officer/installment-collection', 'officer.installment-collection')->name('officer.installment-collection');
    Route::view('officer/installment-tracking', 'officer.installment-tracking')->name('officer.installment-tracking');
});

Route::middleware(['auth', 'role:member'])->group(function () {
    Route::view('member/dashboard', 'member.dashboard')->name('member.dashboard');
    Route::view('member/loans', 'member.loans')->name('member.loans');
    Route::view('member/loan-application', 'member.loan-application')->name('member.loan-application');
    Route::view('member/savings', 'member.savings')->name('member.savings');
});

// Member Management Routes (for managers and field officers)
Route::middleware(['auth', 'role:admin,manager,field_officer'])->group(function () {
    Route::view('members', 'members.index')->name('members.index');
    Route::view('members/register', 'members.register')->name('members.register');
});

Route::view('profile', 'profile')
    ->middleware(['auth'])
    ->name('profile');

require __DIR__.'/auth.php';
