<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SavingAccount extends Model
{
    use HasFactory;

    protected $fillable = [
        'member_id',
        'saving_type',
        'joint_photo',
        'nominee_name',
        'nominee_relation',
        'saving_method',
        'monthly_amount',
        'fdr_amount',
        'start_date',
        'created_by',
        'is_active',
    ];

    protected $casts = [
        'start_date' => 'date',
        'monthly_amount' => 'decimal:2',
        'fdr_amount' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Get the member that owns the saving account.
     */
    public function member(): BelongsTo
    {
        return $this->belongsTo(Member::class);
    }

    /**
     * Get the user who created the saving account.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope for active accounts.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for inactive accounts.
     */
    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    /**
     * Scope for general savings.
     */
    public function scopeGeneral($query)
    {
        return $query->where('saving_type', 'general');
    }

    /**
     * Scope for DPS accounts.
     */
    public function scopeDps($query)
    {
        return $query->where('saving_type', 'dps');
    }

    /**
     * Scope for FDR accounts.
     */
    public function scopeFdr($query)
    {
        return $query->where('saving_type', 'fdr');
    }

    /**
     * Get the account number.
     */
    public function getAccountNumberAttribute(): string
    {
        return 'SAV-' . str_pad($this->id, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Get the saving type label.
     */
    public function getSavingTypeLabelAttribute(): string
    {
        return match($this->saving_type) {
            'general' => 'General Savings',
            'dps' => 'Deposit Pension Scheme',
            'fdr' => 'Fixed Deposit Receipt',
            default => ucfirst($this->saving_type),
        };
    }

    /**
     * Get the saving method label.
     */
    public function getSavingMethodLabelAttribute(): string
    {
        return match($this->saving_method) {
            'daily' => 'Daily',
            'weekly' => 'Weekly',
            default => ucfirst($this->saving_method),
        };
    }
}
